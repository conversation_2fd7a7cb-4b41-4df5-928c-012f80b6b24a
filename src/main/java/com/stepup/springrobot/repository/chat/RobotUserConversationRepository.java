package com.stepup.springrobot.repository.chat;

import com.stepup.springrobot.model.chat.RobotUserConversation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

public interface RobotUserConversationRepository
        extends JpaRepository<RobotUserConversation, Long>, JpaSpecificationExecutor<RobotUserConversation> {
    RobotUserConversation findBySocketSessionId(String socketSessionId);

    Page<RobotUserConversation> findAllByOrderByIdDesc(Pageable pageable);

    RobotUserConversation findFirstByRobotIdAndBotIdOrderByIdDesc(String robotId, Long botId);

    RobotUserConversation findByExternalConversationId(String externalConversationId);

    @Query(value = "SELECT * FROM robot_user_conversation ruc WHERE ruc.log is not null ORDER BY ruc.id DESC", nativeQuery = true)
    Page<RobotUserConversation> findConversationsWithLog(Pageable pageable);

    @Transactional
    @Modifying
    @Query(value = "UPDATE robot_user_conversation SET phone = ?2 WHERE id = ?1", nativeQuery = true)
    void updatePhone(Long conversationId, String phone);

    @Transactional
    @Modifying
    @Query(value = "UPDATE robot_user_conversation SET conversation_reports = ?2 WHERE id = ?1", nativeQuery = true)
    void updateConversationReports(Long conversationId, String conversationReports);

    @Query(value = "SELECT conversation_reports FROM robot_user_conversation WHERE id = ?1", nativeQuery = true)
    String findConversationReportsById(Long conversationId);

    @Query(value = "SELECT id FROM robot_user_conversation " +
                   "WHERE socket_session_id = :socket_session_id", nativeQuery = true)
    Long findConversationIdBySocketSessionId(@Param("socket_session_id") String socketSessionId);

    List<RobotUserConversation> findByIdIn(Collection<Long> ids);
}